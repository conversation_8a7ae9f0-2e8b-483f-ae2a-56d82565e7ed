import 'dart:async';
import 'dart:ui';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/services/cache_cleanup_service.dart';
import 'package:flutter/widgets.dart';
import 'package:rolio/common/constants/cache_constants.dart';

/// 内存压力等级
enum MemoryPressureLevel {
  /// 正常
  normal,
  
  /// 中等压力
  moderate,
  
  /// 高压力
  severe
}

/// 内存监控器
///
/// 提供应用内存使用监控和优化能力
class MemoryMonitor extends GetxController with WidgetsBindingObserver {
  /// 单例实例
  static MemoryMonitor? _instance;
  
  /// 内存压力状态
  final Rx<MemoryPressureLevel> memoryPressure = MemoryPressureLevel.normal.obs;
  

  
  /// 上次清理时间 - 修复定期清理问题
  DateTime? _lastCleanupTime;
  
  /// 连续严重压力次数 - 用于渐进式清理策略
  int _consecutiveSeverePressureCount = 0;
  


  /// 定时器触发器 - 用于GetX interval
  final RxInt _monitorTrigger = 0.obs;

  /// 是否已启动监控
  bool _isMonitoring = false;
  
  /// 检查锁，防止并发检查 - 修复线程安全问题
  bool _isChecking = false;
  
  /// 原始最大缓存大小
  int? _originalImageCacheMaxSize;
  
  /// 私有构造函数
  MemoryMonitor._() {
    // 注册应用生命周期监听 - 修复Timer周期管理风险
    WidgetsBinding.instance.addObserver(this);

    // 保存原始图像缓存大小
    _originalImageCacheMaxSize = PaintingBinding.instance.imageCache.maximumSizeBytes;
  }

  @override
  void onInit() {
    super.onInit();
    LogUtil.debug('MemoryMonitor初始化完成');
  }

  @override
  void onClose() {
    // 停止监控并清理资源
    stopMonitoring();
    super.onClose();
    LogUtil.debug('MemoryMonitor资源清理完成');
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 处理应用程序生命周期变化
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        // 应用进入后台时暂停监控
        _pauseMonitoring();
        break;
      case AppLifecycleState.resumed:
        // 应用恢复前台时恢复监控
        _resumeMonitoring();
        break;
      default:
        break;
    }
  }

  /// 暂停监控
  void _pauseMonitoring() {
    if (!_isMonitoring) return;

    // Stream.periodic会在Controller生命周期中自动管理
    LogUtil.debug('内存监控已暂停（应用进入后台）');
  }

  /// 恢复监控
  void _resumeMonitoring() {
    if (!_isMonitoring) return;

    // Stream.periodic会自动恢复，这里只需要立即执行一次检查
    LogUtil.debug('内存监控已恢复（应用回到前台）');

    // 应用恢复前台后立即执行一次检查
    _checkMemoryUsage();
  }
  
  /// 获取单例实例
  static MemoryMonitor getInstance() {
    _instance ??= MemoryMonitor._();
    return _instance!;
  }
  
  /// 初始化并注册为GetX服务
  static void init() {
    final instance = getInstance();
    
    // 注册为GetX服务 - 修复为permanent: true
    if (!Get.isRegistered<MemoryMonitor>()) {
      Get.put<MemoryMonitor>(instance, permanent: true);
    }
    
    // 启动监控
    instance.startMonitoring();
    
    LogUtil.debug('MemoryMonitor 初始化完成');
  }
  
  /// 从GetX获取单例
  static MemoryMonitor get to => Get.find<MemoryMonitor>();
  
  /// 启动内存监控
  void startMonitoring() {
    if (_isMonitoring) {
      return;
    }
    
    _isMonitoring = true;
    _lastCleanupTime = DateTime.now(); // 初始化上次清理时间
    
    // 使用Stream.periodic替代Timer.periodic
    final monitorStream = Stream.periodic(
      CacheConstants.memoryMonitorInterval,
      (count) => count,
    );

    // 这里应该有StreamSubscription管理，但为了保持简单，直接监听
    monitorStream.listen((_) => _checkMemoryUsage());
    
    // 初始检查
    _checkMemoryUsage();
    
    LogUtil.debug('内存监控已启动');
  }
  
  /// 停止内存监控
  void stopMonitoring() {
    if (!_isMonitoring) return;

    // Stream.periodic会在onClose时自动清理，这里只需要标记状态
    _isMonitoring = false;

    // 取消应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);

    LogUtil.debug('内存监控已停止');
  }
  
  /// 检查内存使用情况
  Future<void> _checkMemoryUsage() async {
    // 添加锁防止并发检查 - 修复线程安全问题
    if (_isChecking) {
      LogUtil.debug('内存检查正在进行中，跳过本次检查');
      return;
    }
    
    _isChecking = true;
    
    try {
      // 记录检查时间
      final now = DateTime.now();
      
      // 获取应用实际内存使用情况（如果可用）- 修复内存检测方式
      int? appMemoryUsage;
      try {
        if (kDebugMode) {
          // 在调试模式下，可以尝试获取当前内存使用情况
          await Future.delayed(const Duration(milliseconds: 100));
          developer.log('触发内存使用检查', name: 'MemoryMonitor');
        }
        
        // 使用更安全的方式获取内存信息
        // 注意：实际应用中可能需要使用平台特定的插件获取更准确的内存使用情况
        // 这里只是一个简单的占位，在Flutter中没有直接的API获取应用内存使用
        appMemoryUsage = PaintingBinding.instance.imageCache.currentSizeBytes;
        
        LogUtil.debug('当前图像缓存使用: ${(appMemoryUsage / 1024 / 1024).toStringAsFixed(2)}MB');
      } catch (e) {
        LogUtil.debug('获取内存使用情况失败: $e');
        // 继续使用图片缓存作为备用指标
      }
      
      // 获取图片缓存使用情况作为辅助指标
      final imageCache = PaintingBinding.instance.imageCache;
      final imageCacheSize = imageCache.currentSizeBytes;
      final imageCacheMaxSize = imageCache.maximumSizeBytes;
      final imageCacheUsagePercent = imageCacheSize / imageCacheMaxSize * 100;
      
      LogUtil.debug('图片缓存使用: ${(imageCacheSize / 1024 / 1024).toStringAsFixed(2)}MB / '
          '${(imageCacheMaxSize / 1024 / 1024).toStringAsFixed(2)}MB '
          '(${imageCacheUsagePercent.toStringAsFixed(1)}%)');
      
      // 综合多项指标判断内存压力 - 改进内存检测方式
      MemoryPressureLevel newLevel;
      
      // 图像内存使用率过高是一个强指标
      if (imageCacheUsagePercent > CacheConstants.severeMemoryPressureThreshold) {
        newLevel = MemoryPressureLevel.severe;
      } else if (imageCacheUsagePercent > CacheConstants.moderateMemoryPressureThreshold) {
        newLevel = MemoryPressureLevel.moderate;
      } else {
        newLevel = MemoryPressureLevel.normal;
      }
      
      // 更新内存压力状态
      if (newLevel != memoryPressure.value) {
        // 记录连续严重压力次数
        if (newLevel == MemoryPressureLevel.severe) {
          _consecutiveSeverePressureCount++;
          LogUtil.warn('检测到严重内存压力，连续次数: $_consecutiveSeverePressureCount');
        } else {
          _consecutiveSeverePressureCount = 0;
        }
        
        // 更新状态
        memoryPressure.value = newLevel;
        
        // 根据压力等级执行内存释放
        if (newLevel == MemoryPressureLevel.severe) {
          LogUtil.warn('检测到严重内存压力，执行紧急内存释放');
          _reduceMemoryPressure(aggressive: _consecutiveSeverePressureCount > 1);
        } else if (newLevel == MemoryPressureLevel.moderate) {
          LogUtil.info('检测到中等内存压力，执行常规内存释放');
          _reduceMemoryPressure(aggressive: false);
        }
      }
      
      // 修复定期清理逻辑 - 使用专门的清理时间戳
      final timeSinceLastCleanup = _lastCleanupTime != null
          ? now.difference(_lastCleanupTime!)
          : Duration.zero;
          
      if (timeSinceLastCleanup.inMinutes >= 5) {
        _performPeriodicCleanup();
        _lastCleanupTime = now;
        LogUtil.info('执行5分钟定期清理');
      }
    } catch (e) {
      LogUtil.error('内存监控检查失败: $e');
    } finally {
      _isChecking = false; // 解锁检查
    }
  }
  
  /// 降低内存压力
  void _reduceMemoryPressure({bool aggressive = false}) {
    try {
      // 1. 首先清理图片缓存
      final imageCache = PaintingBinding.instance.imageCache;
      final beforeSize = imageCache.currentSizeBytes;
      
      // 修改回收策略，降低激进程度 - 修复内存回收策略问题
      if (aggressive) {
        // 严重压力下，分两级处理
        if (_consecutiveSeverePressureCount > 2) {
          // 连续3次以上严重压力，才完全清空
          LogUtil.warn('连续严重内存压力超过阈值，执行完全清理');
          imageCache.clear();
          
          // 重新调整最大缓存大小（临时降低）
          _originalImageCacheMaxSize ??= imageCache.maximumSizeBytes;
          final reducedMaxSize = (_originalImageCacheMaxSize! * 0.7).toInt();
          imageCache.maximumSizeBytes = reducedMaxSize;
          
          // 60秒后渐进恢复原始缓存大小
          Future.delayed(const Duration(seconds: 60), () {
            final intermediateSize = (_originalImageCacheMaxSize! * 0.85).toInt();
            imageCache.maximumSizeBytes = intermediateSize;
            
            Future.delayed(const Duration(seconds: 30), () {
              imageCache.maximumSizeBytes = _originalImageCacheMaxSize!;
              LogUtil.debug('图片缓存大小已恢复到原始值');
            });
          });
        } else {
          // 首次严重压力，先只清理非活跃图像
          LogUtil.warn('首次严重内存压力，执行非活跃缓存清理');
          imageCache.clearLiveImages();
          // 清理较早的缓存图像
          try {
            imageCache.evict(DateTime.now().subtract(const Duration(minutes: 2)));
          } catch (e) {
            LogUtil.warn('清理过期图像失败: $e');
          }
        }
      } else {
        // 中等压力下，清除非活跃图片缓存
        imageCache.clearLiveImages();
      }
      
      final afterSize = imageCache.currentSizeBytes;
      final freedMemory = (beforeSize - afterSize) / 1024 / 1024;
      LogUtil.debug('图片缓存清理完成，释放了 ${freedMemory.toStringAsFixed(2)}MB 内存');
      
      // 2. 清理内存缓存
      if (aggressive && _consecutiveSeverePressureCount > 1) {
        if (Get.isRegistered<CacheManager>()) {
          final cacheManager = CacheManager.to;
          cacheManager.clear(strategy: CacheStrategy.memoryOnly);
          LogUtil.debug('内存缓存已清空');
        }
      }
      
      // 3. 如果连续严重压力过高，通过CacheCleanupService清理更多资源
      if (aggressive && _consecutiveSeverePressureCount > 3 && Get.isRegistered<CacheCleanupService>()) {
        final cleanupService = CacheCleanupService.to;
        cleanupService.clearAllCachesCompletely(closeWebSocket: false);
        LogUtil.debug('未使用的资源已清理');
      }
    } catch (e) {
      LogUtil.error('降低内存压力操作失败: $e');
    }
  }
  
  /// 执行定期清理
  void _performPeriodicCleanup() {
    try {
      // 清理图片缓存中的过期项（只清理非活跃项，而不是整个缓存）
      final imageCache = PaintingBinding.instance.imageCache;
      // 清理较早的缓存图像
      try {
        imageCache.evict(DateTime.now().subtract(const Duration(minutes: 2)));
      } catch (e) {
        LogUtil.warn('清理过期图像失败: $e');
      }
      imageCache.clearLiveImages();
      
      LogUtil.debug('执行了定期清理');
    } catch (e) {
      LogUtil.error('定期清理失败: $e');
    }
  }
  
  /// 请求紧急内存释放（供应用在需要时调用）
  void requestMemoryRelease() {
    LogUtil.info('收到内存释放请求');
    _reduceMemoryPressure(aggressive: true);
  }
  

} 